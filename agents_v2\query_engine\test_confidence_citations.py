#!/usr/bin/env python3
"""
Test script to verify the confidence score and citation functionality
in the enhanced query engine.
"""

import asyncio
import json
from pathlib import Path
import sys
import os

# Add the parent directory to the path so we can import the main module
sys.path.append(str(Path(__file__).parent))

from main import generate_answer, DocumentProcessor, get_or_parse_pdf_content, chunk_text, build_dense_index, build_sparse_index

async def test_generate_answer():
    """Test the enhanced generate_answer function with mock data."""
    print("🧪 Testing enhanced generate_answer function...")
    
    # Mock retrieved chunks
    test_chunks = [
        (0, "The company reported total revenue of $150 million in Q3 2023, representing a 15% increase from the previous quarter."),
        (1, "Net income for the quarter was $25 million, with an operating margin of 18.5%."),
        (2, "The company's cash position remains strong at $75 million as of September 30, 2023.")
    ]
    
    test_query = "What was the company's revenue and profitability in Q3 2023?"
    
    print(f"Query: {test_query}")
    print(f"Retrieved {len(test_chunks)} chunks")
    
    # Test the enhanced generate_answer function
    result = generate_answer(test_query, test_chunks)
    
    print("\n📊 ENHANCED ANSWER RESULT:")
    print("=" * 50)
    
    if result.get("success", False):
        print(f"✅ Success: {result['success']}")
        print(f"📝 Answer: {result['answer']}")
        print(f"🎯 Confidence Score: {result['confidence_score']:.2f} ({result['confidence_score']*100:.1f}%)")
        print(f"💭 Reasoning: {result['reasoning']}")
        print(f"🔗 Context Used: {result['context_used']}")
        
        citations = result.get('citations', [])
        print(f"\n📚 Citations ({len(citations)} sources):")
        for i, citation in enumerate(citations, 1):
            print(f"  {i}. [{citation['citation_id']}] {citation['content_preview']}")
            print(f"     Relevance Rank: {citation['relevance_rank']}")
    else:
        print(f"❌ Failed: {result.get('answer', 'Unknown error')}")
    
    return result

async def test_with_sample_document():
    """Test with a sample document if available."""
    print("\n🧪 Testing with sample document processing...")
    
    # Look for any PDF in the mp_materials directory
    sample_paths = [
        "../../mp_materials/pdfs/form-10-k.pdf",
        "../../mp_materials/pdfs/form-10-q.pdf",
        "../../mp_materials/pdfs/employment-agreement.pdf"
    ]
    
    available_pdf = None
    for path in sample_paths:
        if Path(path).exists():
            available_pdf = path
            break
    
    if not available_pdf:
        print("⚠️ No sample PDFs found. Skipping document test.")
        return None
    
    print(f"📄 Using sample document: {Path(available_pdf).name}")
    
    try:
        # Process a small portion of the document
        content = await get_or_parse_pdf_content(available_pdf)
        
        # Take only first 5000 characters for testing
        test_content = content[:5000]
        chunks = chunk_text(test_content, max_tokens=1000)
        
        print(f"📄 Document processed: {len(chunks)} chunks created")
        
        # Create a simple processor for testing
        processor = DocumentProcessor(available_pdf)
        processor.content = test_content
        processor.chunks = chunks
        
        # Build indices
        processor.faiss_index = build_dense_index(chunks)
        processor.bm25_index = build_sparse_index(chunks)
        
        # Test query
        from main import hybrid_query
        test_query = "What is the company name?"
        retrieved_chunks = hybrid_query(test_query, processor, top_k=3)
        
        print(f"🔍 Query: {test_query}")
        print(f"📊 Retrieved {len(retrieved_chunks)} chunks")
        
        # Generate enhanced answer
        result = generate_answer(test_query, retrieved_chunks)
        
        print("\n📊 DOCUMENT-BASED RESULT:")
        print("=" * 50)
        print(f"📝 Answer: {result['answer']}")
        print(f"🎯 Confidence: {result['confidence_score']:.2f}")
        print(f"💭 Reasoning: {result['reasoning']}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing with document: {e}")
        return None

def format_test_results(results):
    """Format test results for display."""
    print("\n" + "="*60)
    print("🎉 TEST SUMMARY")
    print("="*60)
    
    if results:
        print("✅ Enhanced answer generation is working!")
        print(f"🎯 Average confidence: {results.get('confidence_score', 0):.2f}")
        print(f"📚 Citations generated: {len(results.get('citations', []))}")
        print("🔧 Features verified:")
        print("   ✓ Confidence scoring")
        print("   ✓ Citation generation")
        print("   ✓ Reasoning explanation")
        print("   ✓ Context tracking")
    else:
        print("❌ Tests failed or incomplete")

async def main():
    """Run all tests."""
    print("🚀 Starting Enhanced Query Engine Tests")
    print("="*60)
    
    # Test 1: Mock data test
    mock_result = await test_generate_answer()
    
    # Test 2: Document test (if available)
    doc_result = await test_with_sample_document()
    
    # Summary
    test_result = mock_result or doc_result
    format_test_results(test_result)
    
    print("\n🏁 Testing complete!")

if __name__ == "__main__":
    asyncio.run(main())
